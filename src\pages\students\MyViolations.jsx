import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, List, useNavigate, Modal, Button, Input } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';

const MyViolations = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { error, success } = useNotification();
    const [violations, setViolations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedViolation, setSelectedViolation] = useState(null);
    const [detailVisible, setDetailVisible] = useState(false);
    const [appealVisible, setAppealVisible] = useState(false);
    const [appealReason, setAppealReason] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);

    // Fetch violations
    const fetchViolations = async (pageNum = 1, append = false) => {
        try {
            if (!append) setLoading(true);
            
            const response = await authApi.get(`/violations?page=${pageNum}&limit=10&studentId=${user._id}`);
            console.log('API Response:', response.data); // Debug log
            
            // Handle different response structures
            const newViolations = response.data?.data?.docs || response.data?.violations || response.data?.data || [];
            
            if (append) {
                setViolations(prev => [...prev, ...newViolations]);
            } else {
                setViolations(newViolations);
            }
            
            // Handle different pagination structures
            const paginationData = response.data?.data || response.data?.pagination || response.data;
            setHasMore(paginationData?.hasNextPage || paginationData?.hasNext || false);
            setPage(pageNum);
        } catch (error) {
            console.error('Error fetching violations:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (user?._id) {
            fetchViolations();
        }
    }, [user]);

    // Get violation type label
    const getViolationTypeLabel = (type) => {
        const types = {
            'absent': 'Vắng mặt không phép',
            'late': 'Đi học muộn',
            'uniform': 'Không đúng trang phục',
            'behavior': 'Vi phạm nội quy lớp học',
            'homework': 'Không làm bài tập',
            'phone': 'Sử dụng điện thoại',
            'talking': 'Nói chuyện riêng',
            'eating': 'Ăn uống trong lớp',
            'activity': 'Không tham gia hoạt động',
            'disruption': 'Gây rối trật tự',
            'disrespect': 'Không tôn trọng thầy cô',
            'cheating': 'Gian lận trong thi cử',
            'smoking': 'Hút thuốc',
            'fighting': 'Đánh nhau',
            'vandalism': 'Phá hoại tài sản',
            'other': 'Vi phạm khác',
            'noise': 'Gây ồn ào'
        };
        return types[type] || type;
    };

    // Get status label and color
    const getStatusInfo = (status) => {
        const statusMap = {
            'pending': { label: 'Chờ xử lý', color: '#f39c12', bg: '#fff3cd' },
            'processed': { label: 'Đã xử lý', color: '#28a745', bg: '#d4edda' },
            'appealed': { label: 'Đã khiếu nại', color: '#007bff', bg: '#d1ecf1' },
            'appeal_approved': { label: 'Khiếu nại được chấp nhận', color: '#28a745', bg: '#d4edda' },
            'appeal_rejected': { label: 'Khiếu nại bị từ chối', color: '#dc3545', bg: '#f8d7da' }
        };
        return statusMap[status] || { label: status, color: '#6c757d', bg: '#f8f9fa' };
    };

    // Handle violation detail
    const handleViolationDetail = async (violation) => {
        try {
            const response = await authApi.get(`/violations/${violation._id}`);
            // Handle different response structures
            const violationData = response.data?.data || response.data;
            setSelectedViolation(violationData);
            setDetailVisible(true);
        } catch (error) {
            console.error('Error fetching violation detail:', error);
        }
    };

    // Handle appeal submission
    const handleAppealSubmit = async () => {
        if (appealReason.trim().length < 10) {
            error('Lý do khiếu nại phải có ít nhất 10 ký tự');
            return;
        }

        try {
            setSubmitting(true);
            await authApi.post(`/violations/${selectedViolation._id}/appeal`, {
                appealReason: appealReason.trim()
            });
            
            setAppealVisible(false);
            setAppealReason('');
            setSelectedViolation(null);
            fetchViolations(); // Refresh list
            success('Gửi khiếu nại thành công');
        } catch (err) {
            console.error('Error submitting appeal:', err);
            error('Có lỗi xảy ra khi gửi khiếu nại');
        } finally {
            setSubmitting(false);
        }
    };

    // Load more violations
    const loadMore = () => {
        if (hasMore && !loading) {
            fetchViolations(page + 1, true);
        }
    };

    if (loading && violations.length === 0) {
        return <Loading />;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Vi phạm của tôi"
                showBackButton={true}
                onBackClick={() => navigate('/student')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {violations.length === 0 ? (
                    <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.SUCCESS}</Text>
                        <Text bold style={{ fontSize: '18px', marginBottom: '10px', color: '#28a745' }}>
                            Chưa có vi phạm nào
                        </Text>
                        <Text style={{ color: '#666', fontSize: '14px' }}>
                            Bạn đang có hạnh kiểm tốt. Hãy tiếp tục duy trì!
                        </Text>
                    </Box>
                ) : (
                    <Box style={{ backgroundColor: 'white', borderRadius: '12px', padding: '15px' }}>
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#333' }}>
                                {ICONS.WARNING} Danh sách vi phạm
                            </Text>
                            {violations.length > 0 && (
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    {violations.length} vi phạm
                                </Text>
                            )}
                        </Box>

                        {loading && violations.length === 0 ? (
                            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '40px' }}>
                                <LoadingIndicator />
                            </Box>
                        ) : (
                            <Box>
                                {violations.map((violation, index) => {
                                    const statusInfo = getStatusInfo(violation.status);
                                    return (
                                        <Box
                                            key={violation._id || index}
                                            onClick={() => handleViolationDetail(violation)}
                                            style={{
                                                padding: '15px',
                                                border: '1px solid #e0e0e0',
                                                borderRadius: '8px',
                                                marginBottom: '10px',
                                                backgroundColor: '#fff',
                                                cursor: 'pointer',
                                                transition: 'all 0.2s ease',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.transform = 'translateY(0)';
                                                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                                            }}
                                        >
                                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                                <Box style={{ flex: 1 }}>
                                                    <Text bold style={{ fontSize: '16px', color: '#333', marginBottom: '4px' }}>
                                                        {getViolationTypeLabel(violation.violationType)}
                                                    </Text>
                                                    <Text style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                                                        {violation.description}
                                                    </Text>
                                                </Box>
                                                <Text style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: 'bold', 
                                                    color: '#dc3545',
                                                    marginLeft: '15px'
                                                }}>
                                                    -{violation.pointsDeducted} điểm
                                                </Text>
                                            </Box>
                                            
                                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Text style={{ fontSize: '12px', color: '#999' }}>
                                                    {ICONS.CALENDAR} {formatDistanceToNow(new Date(violation.violationDate), { 
                                                        addSuffix: true, 
                                                        locale: vi 
                                                    })}
                                                </Text>
                                                
                                                <Box style={{
                                                    padding: '6px 10px',
                                                    borderRadius: '16px',
                                                    backgroundColor: statusInfo.bg,
                                                    border: `1px solid ${statusInfo.color}`
                                                }}>
                                                    <Text style={{ fontSize: '11px', color: statusInfo.color, fontWeight: 'bold' }}>
                                                        {statusInfo.label}
                                                    </Text>
                                                </Box>
                                            </Box>
                                        </Box>
                                    );
                                })}

                                {hasMore && (
                                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                                        <Button 
                                            onClick={loadMore}
                                            loading={loading}
                                            style={{ backgroundColor: '#0068ff', color: 'white' }}
                                        >
                                            Tải thêm
                                        </Button>
                                    </Box>
                                )}
                            </Box>
                        )}
                    </Box>
                )}
            </Box>

            {/* Violation Detail Modal */}
            <Modal
                visible={detailVisible}
                title="Chi tiết vi phạm"
                onClose={() => {
                    setDetailVisible(false);
                    setSelectedViolation(null);
                }}
            >
                {selectedViolation && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#0068ff', marginBottom: '10px' }}>
                                {getViolationTypeLabel(selectedViolation.violationType)}
                            </Text>
                            
                            <Box style={{ marginBottom: '15px' }}>
                                <Text bold style={{ fontSize: '14px', marginBottom: '5px' }}>Mô tả:</Text>
                                <Text style={{ fontSize: '14px', color: '#333' }}>
                                    {selectedViolation.description}
                                </Text>
                            </Box>

                            <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Ngày vi phạm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {new Date(selectedViolation.violationDate).toLocaleDateString('vi-VN')}
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Điểm trừ:
                                    </Text>
                                    <Text style={{ fontSize: '14px', color: '#dc3545', fontWeight: 'bold' }}>
                                        -{selectedViolation.pointsDeducted} điểm
                                    </Text>
                                </Box>
                            </Box>

                            {selectedViolation.reportedBy && (
                                <Box style={{ marginBottom: '15px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Người báo cáo:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.reportedBy.name}
                                    </Text>
                                </Box>
                            )}

                            {selectedViolation.appealReason && (
                                <Box style={{ marginBottom: '15px', padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lý do khiếu nại:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.appealReason}
                                    </Text>
                                </Box>
                            )}
                        </Box>

                        {selectedViolation.status === 'processed' && !selectedViolation.appealReason && (
                            <Button
                                fullWidth
                                style={{ backgroundColor: '#f39c12', color: 'white', marginTop: '10px' }}
                                onClick={() => {
                                    setDetailVisible(false);
                                    setAppealVisible(true);
                                }}
                            >
                                {ICONS.APPEAL} Gửi khiếu nại
                            </Button>
                        )}
                    </Box>
                )}
            </Modal>

            {/* Appeal Modal */}
            <Modal
                visible={appealVisible}
                title="Gửi khiếu nại"
                onClose={() => {
                    setAppealVisible(false);
                    setAppealReason('');
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { 
                        text: submitting ? 'Đang gửi...' : 'Gửi khiếu nại', 
                        close: false, 
                        onClick: handleAppealSubmit,
                        disabled: submitting || appealReason.length < 10
                    }
                ]}
            >
                <Box style={{ padding: '20px' }}>
                    <Text style={{ marginBottom: '15px', fontSize: '14px', color: '#666' }}>
                        Vui lòng nêu rõ lý do khiếu nại vi phạm này. Khiếu nại sẽ được xem xét và phản hồi trong thời gian sớm nhất.
                    </Text>
                    
                    <Input.TextArea
                        placeholder="Nhập lý do khiếu nại (tối thiểu 10 ký tự)..."
                        value={appealReason}
                        onChange={(e) => setAppealReason(e.target.value)}
                        rows={4}
                        style={{ marginBottom: '10px' }}
                    />
                    
                    <Text style={{ fontSize: '12px', color: appealReason.length < 10 ? '#dc3545' : '#28a745' }}>
                        {appealReason.length}/10 ký tự tối thiểu
                    </Text>
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default MyViolations;
