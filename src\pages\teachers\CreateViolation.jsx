import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, useNavigate, Button, Input, Select } from 'zmp-ui';

const { Option } = Select;
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import useNotification from '../../hooks/useNotification';
import { useSchoolYear } from '../../context/SchoolYearContext';

const CreateViolation = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { success, error } = useNotification();
    const schoolYear = useSchoolYear();
    const [loading, setLoading] = useState(false);
    const [classes, setClasses] = useState([]);
    const [students, setStudents] = useState([]);
    const [violationTypes, setViolationTypes] = useState([]);
    const [formData, setFormData] = useState({
        classId: '',
        studentId: '',
        violationType: '',
        description: '',
        violationDate: new Date().toISOString().split('T')[0],
        location: ''
    });
    const [errors, setErrors] = useState({});
    const [submitting, setSubmitting] = useState(false);

    // Fetch initial data
    useEffect(() => {
        fetchInitialData();
    }, []);

    // Debug state changes
    useEffect(() => {
        console.log('Classes state updated:', classes);
    }, [classes]);

    useEffect(() => {
        console.log('ViolationTypes state updated:', violationTypes);
    }, [violationTypes]);

    const fetchInitialData = async () => {
        try {
            setLoading(true);
            
            console.log('Starting API calls...');
            
            // Fetch classes and violation config
            const [classesRes, configRes] = await Promise.all([
                authApi.get('/directory/teacher/classes'),
                authApi.get('/violations/config')
            ]);
            
            console.log('Classes response full:', classesRes);
            console.log('Classes response data:', classesRes.data);
            console.log('Config response full:', configRes);
            console.log('Config response data:', configRes.data);
            
            const classesData = Array.isArray(classesRes.data.data) ? classesRes.data.data : [];
            const violationTypesData = Array.isArray(configRes.data?.data?.violationTypes) ? configRes.data.data.violationTypes : [];
            
            console.log('Processed classes data:', classesData);
            console.log('Processed violation types data:', violationTypesData);
            console.log('Is classesRes.data an array?', Array.isArray(classesRes.data));
            console.log('Is classesRes.data.data an array?', Array.isArray(classesRes.data.data));
            console.log('Type of classesRes.data:', typeof classesRes.data);
            console.log('classesRes.data.data:', classesRes.data.data);
            
            setClasses(classesData);
            setViolationTypes(violationTypesData);
            
            console.log('State should be set now');
        } catch (error) {
            console.error('Error fetching initial data:', error);
            // Ensure arrays are set even on error
            setClasses([]);
            setViolationTypes([]);
        } finally {
            setLoading(false);
        }
    };

    // Fetch students when class changes
    useEffect(() => {
        if (formData.classId) {
            fetchStudents(formData.classId);
        } else {
            setStudents([]);
            setFormData(prev => ({ ...prev, studentId: '' }));
        }
    }, [formData.classId]);

    const fetchStudents = async (classId) => {
        try {
            console.log('Fetching students for class:', classId);
            const response = await authApi.get(`/directory/class/${classId}`);
            console.log('Students API response:', response.data);
            console.log('Students array:', response.data.data?.students);
            
            const studentsData = Array.isArray(response.data.data?.students) ? response.data.data.students : [];
            console.log('Setting students:', studentsData);
            setStudents(studentsData);
        } catch (error) {
            console.error('Error fetching students:', error);
            setStudents([]);
        }
    };

    // Handle form input changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    // Validate form
    const validateForm = () => {
        const newErrors = {};

        if (!formData.classId) newErrors.classId = 'Vui lòng chọn lớp học';
        if (!formData.studentId) newErrors.studentId = 'Vui lòng chọn học sinh';
        if (!formData.violationType) newErrors.violationType = 'Vui lòng chọn loại vi phạm';
        if (!formData.description.trim()) newErrors.description = 'Vui lòng nhập mô tả vi phạm';
        if (formData.description.trim().length < 5) newErrors.description = 'Mô tả phải có ít nhất 5 ký tự';
        if (!formData.violationDate) newErrors.violationDate = 'Vui lòng chọn ngày vi phạm';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) return;

        try {
            setSubmitting(true);
            
            const submitData = {
                ...formData,
                schoolYear: schoolYear || '2024-2025'
            };

            await authApi.post('/violations', submitData);
            
            success('Tạo báo cáo vi phạm thành công');
            navigate('/teacher');
        } catch (err) {
            console.error('Error creating violation:', err);
            error('Có lỗi xảy ra khi tạo báo cáo vi phạm');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Tạo báo cáo vi phạm"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '20px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '18px', marginBottom: '20px', color: '#0068ff', textAlign: 'center' }}>
                        {ICONS.REPORT} Báo cáo vi phạm học sinh
                    </Text>

                    {/* Class Selection */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Lớp học <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        
                        {/* Debug info */}
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                            Debug: Có {classes.length} lớp học
                        </Text>
                        
                        <Select
                            placeholder="Chọn lớp học"
                            value={formData.classId}
                            onChange={(value) => handleInputChange('classId', value)}
                            style={{ marginBottom: '15px' }}
                        >
                            {classes.map(cls => (
                                <Option 
                                    key={cls.id} 
                                    value={cls.id} 
                                    title={cls.name}
                                >
                                    {cls.name} - {cls.classRoom || ''}
                                </Option>
                            ))}
                        </Select>
                        {errors.classId && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.classId}
                            </Text>
                        )}
                    </Box>

                    {/* Student Selection */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Học sinh <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        
                        {/* Debug info */}
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                            Debug: Có {students.length} học sinh
                        </Text>
                        
                        {formData.classId && students.length === 0 && (
                            <Text style={{ fontSize: '12px', color: '#ffa500', marginBottom: '5px' }}>
                                Lớp học này chưa có học sinh nào
                            </Text>
                        )}
                        
                        <Select
                            placeholder={students.length === 0 ? "Không có học sinh" : "Chọn học sinh"}
                            value={formData.studentId}
                            onChange={(value) => handleInputChange('studentId', value)}
                            disabled={!formData.classId || students.length === 0}
                        >
                            {students.map(student => (
                                <Option 
                                    key={student._id || student.id} 
                                    value={student._id || student.id} 
                                    title={`${student.name} (${student.studentId || ''})`} 
                                />
                            ))}
                        </Select>
                        {errors.studentId && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.studentId}
                            </Text>
                        )}
                    </Box>

                    {/* Violation Type */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Loại vi phạm <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        
                        {/* Debug info */}
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                            Debug: Có {violationTypes.length} loại vi phạm
                        </Text>
                        
                        <Select
                            placeholder="Chọn loại vi phạm"
                            value={formData.violationType}
                            onChange={(value) => handleInputChange('violationType', value)}
                            style={{ marginBottom: '15px' }}
                        >
                            {violationTypes.map(type => (
                                <Option 
                                    key={type.code} 
                                    value={type.code} 
                                    title={type.label}
                                >
                                    {type.label} (-{type.points} điểm)
                                </Option>
                            ))}
                        </Select>
                        {errors.violationType && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.violationType}
                            </Text>
                        )}
                    </Box>

                    {/* Description */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Mô tả chi tiết <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        <Input.TextArea
                            placeholder="Mô tả chi tiết về vi phạm (tối thiểu 5 ký tự)..."
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            rows={4}
                        />
                        <Text style={{ 
                            fontSize: '12px', 
                            color: formData.description.length < 5 ? '#dc3545' : '#28a745',
                            marginTop: '5px'
                        }}>
                            {formData.description.length}/5 ký tự tối thiểu
                        </Text>
                        {errors.description && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.description}
                            </Text>
                        )}
                    </Box>

                    {/* Violation Date */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Ngày vi phạm <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        <Input
                            type="date"
                            value={formData.violationDate}
                            onChange={(e) => handleInputChange('violationDate', e.target.value)}
                            max={new Date().toISOString().split('T')[0]}
                        />
                        {errors.violationDate && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.violationDate}
                            </Text>
                        )}
                    </Box>

                    {/* Location (Optional) */}
                    <Box style={{ marginBottom: '30px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Địa điểm vi phạm
                        </Text>
                        <Input
                            placeholder="Ví dụ: Lớp học, sân trường, hành lang..."
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                        />
                    </Box>

                    {/* Submit Button */}
                    <Button
                        fullWidth
                        onClick={handleSubmit}
                        loading={submitting}
                        disabled={submitting}
                        style={{
                            backgroundColor: '#dc3545',
                            color: 'white',
                            height: '48px',
                            fontSize: '16px',
                            fontWeight: 'bold'
                        }}
                    >
                        {submitting ? 'Đang tạo báo cáo...' : `${ICONS.REPORT} Tạo báo cáo vi phạm`}
                    </Button>
                </Box>

                {/* Info Card */}
                <Box style={{
                    backgroundColor: '#fff3cd',
                    borderRadius: '12px',
                    padding: '15px',
                    marginTop: '20px',
                    border: '1px solid #ffeaa7'
                }}>
                    <Text style={{ fontSize: '14px', color: '#856404', textAlign: 'center' }}>
                        {ICONS.TIP} <Text bold>Lưu ý:</Text> Báo cáo vi phạm sẽ được gửi thông báo đến phụ huynh và ảnh hưởng đến điểm thi đua của học sinh.
                    </Text>
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default CreateViolation;
