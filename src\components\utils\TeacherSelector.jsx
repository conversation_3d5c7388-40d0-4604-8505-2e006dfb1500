import React, { useState, useEffect } from 'react';
import { Box, Text, Select, Input } from 'zmp-ui';
import { authApi } from '../../utils/api';
import useNotification from '../../hooks/useNotification';
import { DEPARTMENT_GROUPS } from '../../constants/exam';

const { Option } = Select;

const TeacherSelector = ({ 
    value, 
    onChange, 
    label = "Chọn giáo viên",
    required = false,
    disabled = false,
    style = {}
}) => {
    const notification = useNotification();
    const [teachers, setTeachers] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });
    const [filters, setFilters] = useState({
        search: '',
        department: '',
        page: 1,
        limit: 20
    });
    const [loading, setLoading] = useState(false);

    // Fetch teachers list with pagination and filters
    const fetchTeachers = async () => {
        setLoading(true);
        try {
            const queryParams = new URLSearchParams({
                page: filters.page.toString(),
                limit: filters.limit.toString(),
                ...(filters.search && { search: filters.search }),
                ...(filters.department && { department: filters.department })
            });

            const response = await authApi.get(`/directory/teachers?${queryParams}`);
            if (response.data) {
                setTeachers(response.data.teachers || []);
                setPagination(response.data.pagination || {
                    current: 1,
                    pages: 1,
                    total: 0
                });
            }
        } catch (error) {
            console.error('Error fetching teachers:', error);
            notification.showError('Lỗi', 'Không thể tải danh sách giáo viên');
        } finally {
            setLoading(false);
        }
    };

    // Handle filter changes
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: 1 // Reset to first page when filters change
        }));
    };

    // Fetch teachers with debounce
    useEffect(() => {
        const timer = setTimeout(() => {
            fetchTeachers();
        }, 500);

        return () => clearTimeout(timer);
    }, [filters]);

    // Reset filters when value is cleared
    useEffect(() => {
        if (!value) {
            setFilters(prev => ({
                ...prev,
                search: '',
                department: '',
                page: 1
            }));
        }
    }, [value]);

    return (
        <Box style={style}>
            {/* Department Filter */}
            <Box style={{ marginBottom: '15px' }}>
                <Text style={{ marginBottom: '5px' }}>Bộ môn</Text>
                <Select
                    value={filters.department}
                    onChange={(value) => handleFilterChange('department', value)}
                    style={{ marginBottom: '10px' }}
                    disabled={disabled}
                >
                    <Option value="" title="Tất cả bộ môn" />
                    {Object.entries(DEPARTMENT_GROUPS).map(([key, label]) => (
                        <Option key={key} value={label} title={label} />
                    ))}
                </Select>

                {/* Search Input */}
                <Text style={{ marginBottom: '5px' }}>Tìm kiếm giáo viên</Text>
                <Input
                    placeholder="Nhập tên giáo viên..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    style={{ marginBottom: '10px' }}
                    disabled={disabled}
                />

                {/* Teacher Selection */}
                <Text style={{ marginBottom: '5px' }}>
                    {label} {required && <Text style={{ color: 'red' }}>*</Text>}
                </Text>
                <Select
                    value={value}
                    onChange={onChange}
                    style={{ marginBottom: '15px' }}
                    disabled={disabled || loading}
                    placeholder={loading ? "Đang tải..." : "Chọn giáo viên"}
                >
                    {teachers.map(teacher => (
                        <Option 
                            key={teacher._id} 
                            value={teacher._id} 
                            title={`${teacher.name || 'Chưa có tên'}${teacher.department ? ` - ${teacher.department}` : ''}`} 
                        />
                    ))}
                </Select>

                {/* Pagination Info */}
                <Text style={{ fontSize: '12px', color: '#666', textAlign: 'right' }}>
                    Trang {pagination.current}/{pagination.pages} ({pagination.total} giáo viên)
                </Text>
            </Box>
        </Box>
    );
};

export default TeacherSelector; 