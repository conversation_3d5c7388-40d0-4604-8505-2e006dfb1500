import React, { useContext, useEffect, useState } from 'react';
import { Box, Text, Select, <PERSON><PERSON>, Header, Page, List, Modal } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';
import { useSchoolYear } from '../context/SchoolYearContext';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { ICONS } from '../constants/icons';
import Loading from '../components/utils/Loading';
import useNotification from '../hooks/useNotification';

const { Option } = Select;

const getGreetingWithIcon = (user) => {
    if (!user) return { text: "Chào mừng bạn!", icon: ICONS.USER };
    
    const name = user.displayName || user.name || 'bạn';
    const isMale = user.gender === 'male' || 
                   user.username?.toLowerCase().includes('nam') ||
                   user.displayName?.toLowerCase().includes('nam');
    
    if (user.role?.includes('teacher')) {
        return {
            text: `Chào ${name}! ${ICONS.DONE}`,
            icon: isMale ? ICONS.TEACHER : ICONS.TEACHER_FEMALE
        };
    } else if (user.role?.includes('student')) {
        return {
            text: `Chào ${name}! ${ICONS.DONE}`, 
            icon: isMale ? ICONS.STUDENT : ICONS.STUDENT_FEMALE
        };
    } else {
        return {
            text: `Chào ${name}! ${ICONS.DONE}`,
            icon: isMale ? ICONS.ADMIN : ICONS.ADMIN
        };
    }
};


const SchoolYearSelection = () => {
    const navigate = useNavigate();
    const { schoolYears, selectedSchoolYear, setSelectedSchoolYear, loading } = useSchoolYear();
    const { user, fetchUser, login } = useContext(AuthContext);
    const { error } = useNotification();
    const [hasUserInteracted, setHasUserInteracted] = useState(false);
    const [selecting, setSelecting] = useState(false);

    useEffect(() => {
        // Chỉ tự động chuyển hướng nếu user đã tương tác (chọn school year) hoặc có school year từ localStorage
        const storedSchoolYear = localStorage.getItem('selectedSchoolYear');

        if (selectedSchoolYear && user && (hasUserInteracted || storedSchoolYear)) {
            const handleAutoRedirect = async () => {
                try {
                    console.log('SchoolYearSelection: Auto redirect with school year:', selectedSchoolYear);

                    // Save to localStorage first
                    localStorage.setItem('selectedSchoolYear', selectedSchoolYear);

                    // Call API with selected school year to get fresh data
                    const response = await authApi.get(`/auth/me?schoolYear=${selectedSchoolYear}`);
                    const userData = response.data;

                    console.log('SchoolYearSelection: Auto redirect user data:', userData);

                    // Update user data in AuthContext with fresh data
                    await fetchUser();

                    // Check for missing class info and set flag
                    if (userData.role.includes('student') && !userData.class) {
                        localStorage.setItem('showNoClassModal', 'true');
                    }

                    // Navigate based on role
                    const isTeacher = userData.role.includes('teacher') || userData.role.includes('TEACHER');
                    const redirectPath = isTeacher ? '/teacher' : '/student';
                    navigate(redirectPath, { replace: true });
                } catch (error) {
                    console.error('SchoolYearSelection: Auto redirect error:', error);
                    // If API fails, still navigate but user might see issues in target page
                    const isTeacher = user.role.includes('teacher') || user.role.includes('TEACHER');
                    const redirectPath = isTeacher ? '/teacher' : '/student';
                    navigate(redirectPath, { replace: true });
                }
            };

            handleAutoRedirect();
        }
    }, [selectedSchoolYear, user, navigate, fetchUser, hasUserInteracted]);

    const handleContinue = async () => {
        if (selectedSchoolYear) {
            try {
                console.log('SchoolYearSelection: Manual continue with school year:', selectedSchoolYear);

                // Mark user interaction
                setHasUserInteracted(true);

                // Save selected school year to localStorage first
                localStorage.setItem('selectedSchoolYear', selectedSchoolYear);

                // Update the context state to ensure synchronization
                setSelectedSchoolYear(selectedSchoolYear);

                // Call API directly with the selected school year to get fresh data
                const response = await authApi.get(`/auth/me?schoolYear=${selectedSchoolYear}`);
                const userData = response.data;

                console.log('SchoolYearSelection: Manual continue user data:', userData);

                if (!userData) {
                    error('Có lỗi xảy ra khi lấy thông tin người dùng');
                    return;
                }

                // Update user data in AuthContext with fresh data
                await fetchUser();

                // Navigate based on role - always navigate, let the target page handle missing class info
                const isTeacher = userData.role.includes('teacher') || userData.role.includes('TEACHER');
                const redirectPath = isTeacher ? '/teacher' : '/student';

                // Store class info status for StudentEdu to check
                if (userData.role.includes('student') && !userData.class) {
                    localStorage.setItem('showNoClassModal', 'true');
                }

                navigate(redirectPath, { replace: true });
            } catch (error) {
                console.error('Error fetching user data:', error);
                error('Có lỗi xảy ra khi lấy thông tin người dùng');
            }
        }
    };

    const greeting = getGreetingWithIcon(user);
    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: 'white' }}>
            {/* Header with responsive design */}
            <Box
                className="header"
                style={{
                    backgroundColor: '#0068ff',
                    color: 'white',
                    padding: 'clamp(20px, 5vw, 30px)',
                    textAlign: 'center',
                    position: 'relative',
                    minHeight: 'clamp(120px, 25vh, 150px)',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: 'clamp(20px, 5vw, 30px)',
                    borderRadius: '12px',
                    // Mobile-first responsive styles
                    '@media (max-width: 480px)': {
                        padding: '16px',
                        minHeight: '100px',
                        marginBottom: '16px',
                        borderRadius: '8px',
                    }
                }}
            >
                {/* Icon và text trong cùng một dòng */}
                <Box
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '12px',
                        flexWrap: 'wrap', // Cho phép xuống dòng trên màn hình nhỏ
                        marginBottom: '8px'
                    }}
                >
                    <Text
                        style={{
                            fontSize: 'clamp(24px, 6vw, 32px)', // Responsive font size
                            lineHeight: '1.2'
                        }}
                    >
                        {greeting.icon}
                    </Text>
                    <Text
                        className="welcome-text"
                        bold
                        style={{
                            fontSize: 'clamp(18px, 4.5vw, 24px)', // Responsive font size
                            lineHeight: '1.3',
                            textAlign: 'center',
                            wordBreak: 'break-word' // Tránh text bị tràn
                        }}
                    >
                        {greeting.text}
                    </Text>
                </Box>

                <Text
                    className="subtitle"
                    style={{
                        fontSize: 'clamp(14px, 3.5vw, 18px)',
                        opacity: 0.9,
                        lineHeight: '1.4',
                        textAlign: 'center',
                        maxWidth: '90%' // Giới hạn width để tránh text quá dài
                    }}
                >
                    Vui lòng chọn năm học để tiếp tục
                </Text>
            </Box>

            {/* Main Content với responsive design */}
            <Box
                className="main-content"
                flex
                flexDirection="column"
                style={{
                    flex: 1,
                    padding: '0 clamp(8px, 2vw, 16px)' // Responsive padding
                }}
            >
                <Box
                    className="illustration"
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: 'clamp(16px, 4vw, 20px) 0',
                    }}
                >
                    <img
                        src="/assets/images/logo-tunghia1.jpg"
                        alt="THPT Số 1 Tư Nghĩa"
                        style={{
                            maxWidth: 'clamp(60%, 15vw, 70%)',
                            height: 'auto',
                            borderRadius: '8px' // Thêm border radius cho đẹp
                        }}
                    />
                </Box>

                <Box className="form-group" style={{ marginBottom: 'clamp(20px, 5vw, 30px)' }}>
                    <Text
                        className="form-label"
                        bold
                        style={{
                            marginBottom: '12px',
                            color: '#555',
                            fontSize: 'clamp(14px, 3.5vw, 16px)',
                            textAlign: 'center'
                        }}
                    >
                        Chọn năm học
                    </Text>
                    <Select
                        value={selectedSchoolYear}
                        onChange={(value) => {
                            setSelectedSchoolYear(value);
                            setHasUserInteracted(true);
                        }}
                        placeholder="Chọn năm học"
                        loading={loading}
                        style={{
                            width: '100%',
                            minHeight: '44px', // Tối thiểu 44px cho touch target
                            fontSize: 'clamp(14px, 3.5vw, 16px)'
                        }}
                    >
                        {schoolYears.map(year => (
                            <Option key={year} value={year} title={year} />
                        ))}
                    </Select>
                </Box>

                <Button
                    fullWidth
                    variant="primary"
                    onClick={handleContinue}
                    disabled={!selectedSchoolYear || loading}
                    style={{
                        marginTop: 'clamp(16px, 4vw, 20px)',
                        minHeight: '48px', // Touch-friendly height
                        fontSize: 'clamp(14px, 3.5vw, 16px)',
                        borderRadius: '8px'
                    }}
                >
                    Tiếp tục
                </Button>
            </Box>

            <Box
                className="footer"
                style={{
                    padding: 'clamp(16px, 4vw, 20px)',
                    textAlign: 'center',
                    fontSize: 'clamp(10px, 2.5vw, 12px)',
                    color: '#888',
                    lineHeight: '1.4'
                }}
            >
                © 2025 THPT Số 1 Tư Nghĩa - Tất cả quyền được bảo lưu
            </Box>
        </Box>
    );
};

export default SchoolYearSelection; 